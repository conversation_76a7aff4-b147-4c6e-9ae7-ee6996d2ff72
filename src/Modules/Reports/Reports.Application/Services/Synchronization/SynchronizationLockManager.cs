using Microsoft.Extensions.Logging;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Application.Services.Synchronization;

public class SynchronizationLockManager
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly ILogger<SynchronizationLockManager> _logger;
    
    // Configuration constants based on user preferences
    private const int BatchSize = 25;
    private static readonly TimeSpan LockTimeout = TimeSpan.FromMinutes(2);

    public SynchronizationLockManager(
        IReportsUnitOfWork unitOfWork,
        ILogger<SynchronizationLockManager> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<List<UrbetrackSynchronization>> AcquireBatchAsync(
        SynchronizationStatus status,
        CancellationToken cancellationToken = default)
    {
        var lockIdentifier = Guid.NewGuid().ToString();
        
        _logger.LogInformation(
            "Attempting to acquire lock batch for status {Status} with identifier {LockIdentifier}",
            status, lockIdentifier);

        try
        {
            var lockedEntries = await _unitOfWork.UrbetrackSynchronizations
                .AcquireLockBatchAsync(status, BatchSize, lockIdentifier, cancellationToken);

            if (lockedEntries.Any())
            {
                // Mark entries as processing
                foreach (var entry in lockedEntries)
                {
                    entry.StartProcessing();
                }

                await _unitOfWork.UrbetrackSynchronizations.BulkUpdateAsync(lockedEntries, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation(
                    "Successfully acquired and marked {Count} entries as processing with lock identifier {LockIdentifier}",
                    lockedEntries.Count, lockIdentifier);
            }
            else
            {
                _logger.LogInformation(
                    "No available entries found for status {Status} to acquire locks",
                    status);
            }

            return lockedEntries;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to acquire lock batch for status {Status} with identifier {LockIdentifier}",
                status, lockIdentifier);
            throw;
        }
    }

    public async Task ReleaseBatchAsync(
        IEnumerable<UrbetrackSynchronization> entries,
        CancellationToken cancellationToken = default)
    {
        var entriesList = entries.ToList();
        
        if (!entriesList.Any())
            return;

        var lockIdentifiers = entriesList
            .Where(e => !string.IsNullOrEmpty(e.LockedBy))
            .Select(e => e.LockedBy)
            .Distinct()
            .ToList();

        _logger.LogInformation(
            "Releasing locks for {Count} entries with identifiers: {LockIdentifiers}",
            entriesList.Count, string.Join(", ", lockIdentifiers));

        try
        {
            await _unitOfWork.UrbetrackSynchronizations
                .ReleaseLockBatchAsync(entriesList, cancellationToken);

            _logger.LogInformation(
                "Successfully released locks for {Count} entries",
                entriesList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to release locks for {Count} entries with identifiers: {LockIdentifiers}",
                entriesList.Count, string.Join(", ", lockIdentifiers));
            throw;
        }
    }

    public async Task<int> CleanupStaleLocksAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Starting cleanup of stale locks older than {LockTimeout}",
            LockTimeout);

        try
        {
            var cleanedCount = await _unitOfWork.UrbetrackSynchronizations
                .CleanupStaleLocks(LockTimeout, cancellationToken);

            if (cleanedCount > 0)
            {
                _logger.LogWarning(
                    "Cleaned up {Count} stale locks that exceeded timeout of {LockTimeout}",
                    cleanedCount, LockTimeout);
            }
            else
            {
                _logger.LogInformation("No stale locks found during cleanup");
            }

            return cleanedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup stale locks");
            throw;
        }
    }

    public async Task<ProcessingBatch> AcquireAndProcessBatchAsync(
        SynchronizationStatus status,
        Func<List<UrbetrackSynchronization>, CancellationToken, Task> processingFunc,
        CancellationToken cancellationToken = default)
    {
        var batch = await AcquireBatchAsync(status, cancellationToken);
        
        if (!batch.Any())
        {
            return new ProcessingBatch(new List<UrbetrackSynchronization>(), 0);
        }

        var processedCount = 0;
        try
        {
            await processingFunc(batch, cancellationToken);
            processedCount = batch.Count;
            
            _logger.LogInformation(
                "Successfully processed batch of {Count} entries for status {Status}",
                batch.Count, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error processing batch of {Count} entries for status {Status}",
                batch.Count, status);
            throw;
        }
        finally
        {
            // Always release locks, even if processing failed
            await ReleaseBatchAsync(batch, cancellationToken);
        }

        return new ProcessingBatch(batch, processedCount);
    }
}

public record ProcessingBatch(List<UrbetrackSynchronization> Entries, int ProcessedCount);