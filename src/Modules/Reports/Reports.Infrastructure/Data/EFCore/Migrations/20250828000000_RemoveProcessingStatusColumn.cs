using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class RemoveProcessingStatusColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop the old composite index that included ProcessingStatus
            migrationBuilder.DropIndex(
                name: "IX_UrbetrackSync_ProcessingStatus_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            // Drop the ProcessingStatus constraint
            migrationBuilder.DropCheckConstraint(
                name: "CK_ProcessingStatus_ValidValues",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            // Remove the ProcessingStatus column
            migrationBuilder.DropColumn(
                name: "REPEM14_Estado_Procesamiento",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            // Create new index on Status only (replacing the composite index)
            migrationBuilder.CreateIndex(
                name: "IX_UrbetrackSync_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                column: "REPEM14_Estado");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop the new Status-only index
            migrationBuilder.DropIndex(
                name: "IX_UrbetrackSync_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            // Re-add the ProcessingStatus column
            migrationBuilder.AddColumn<string>(
                name: "REPEM14_Estado_Procesamiento",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                type: "text",
                nullable: false,
                defaultValue: "Available");

            // Re-create the ProcessingStatus constraint
            migrationBuilder.AddCheckConstraint(
                name: "CK_ProcessingStatus_ValidValues",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                sql: "\"REPEM14_Estado_Procesamiento\" IN ('Available', 'Locked', 'Processing')");

            // Re-create the composite index
            migrationBuilder.CreateIndex(
                name: "IX_UrbetrackSync_ProcessingStatus_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                columns: new[] { "REPEM14_Estado_Procesamiento", "REPEM14_Estado" });
        }
    }
}
