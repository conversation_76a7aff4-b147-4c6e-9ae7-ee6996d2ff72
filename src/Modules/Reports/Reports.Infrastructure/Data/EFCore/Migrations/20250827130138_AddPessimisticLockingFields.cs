using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class AddPessimisticLockingFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "REPEM14_Bloqueado_En",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "REPEM14_Bloqueado_Por",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "REPEM14_Estado_Procesamiento",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                type: "text",
                nullable: false,
                defaultValue: "Available");

            migrationBuilder.CreateIndex(
                name: "IX_UrbetrackSync_LockedAt",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                column: "REPEM14_Bloqueado_En");

            migrationBuilder.CreateIndex(
                name: "IX_UrbetrackSync_ProcessingStatus_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack",
                columns: new[] { "REPEM14_Estado_Procesamiento", "REPEM14_Estado" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UrbetrackSync_LockedAt",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropIndex(
                name: "IX_UrbetrackSync_ProcessingStatus_Status",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropColumn(
                name: "REPEM14_Bloqueado_En",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropColumn(
                name: "REPEM14_Bloqueado_Por",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");

            migrationBuilder.DropColumn(
                name: "REPEM14_Estado_Procesamiento",
                table: "Reporting-Emvarias_14-Sincronizacion_Urbetrack");
        }
    }
}
