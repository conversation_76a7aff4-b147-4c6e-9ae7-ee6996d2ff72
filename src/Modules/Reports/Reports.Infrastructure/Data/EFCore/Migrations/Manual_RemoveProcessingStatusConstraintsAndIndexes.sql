-- Manual SQL Migration Script for Removing ProcessingStatus Constraints and Indexes
-- Migration: RemoveProcessingStatusConstraintsAndIndexes
-- Date: 2025-08-28
-- Description: Removes ProcessingStatus-related constraints and indexes, updates existing data to use new SynchronizationStatus intermediate states

-- =====================================================
-- DATA MIGRATION: Convert existing ProcessingStatus to SynchronizationStatus
-- =====================================================

-- Update records that are currently 'Locked' to appropriate locked states based on their current SynchronizationStatus
UPDATE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
SET "REPEM14_Estado" = CASE 
    WHEN "REPEM14_Estado" = 'PendingCreation' AND "REPEM14_Estado_Procesamiento" = 'Locked' THEN 'LockedForCreation'
    WHEN "REPEM14_Estado" = 'PendingModification' AND "REPEM14_Estado_Procesamiento" = 'Locked' THEN 'LockedForModification'
    WHEN "REPEM14_Estado" = 'PendingCancellation' AND "REPEM14_Estado_Procesamiento" = 'Locked' THEN 'LockedForCancellation'
    ELSE "REPEM14_Estado"
END
WHERE "REPEM14_Estado_Procesamiento" = 'Locked';

-- Update records that are currently 'Processing' to appropriate processing states based on their current SynchronizationStatus
UPDATE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
SET "REPEM14_Estado" = CASE 
    WHEN "REPEM14_Estado" = 'PendingCreation' AND "REPEM14_Estado_Procesamiento" = 'Processing' THEN 'ProcessingCreation'
    WHEN "REPEM14_Estado" = 'PendingModification' AND "REPEM14_Estado_Procesamiento" = 'Processing' THEN 'ProcessingModification'
    WHEN "REPEM14_Estado" = 'PendingCancellation' AND "REPEM14_Estado_Procesamiento" = 'Processing' THEN 'ProcessingCancellation'
    ELSE "REPEM14_Estado"
END
WHERE "REPEM14_Estado_Procesamiento" = 'Processing';

-- =====================================================
-- REMOVE CONSTRAINTS AND INDEXES
-- =====================================================

-- Drop the ProcessingStatus constraint
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
DROP CONSTRAINT IF EXISTS "CK_ProcessingStatus_ValidValues";

-- Drop the old composite index that included ProcessingStatus
DROP INDEX IF EXISTS "IX_UrbetrackSync_ProcessingStatus_Status";

-- =====================================================
-- CREATE NEW INDEXES
-- =====================================================

-- Create new index on Status only (replacing the composite index)
CREATE INDEX IF NOT EXISTS "IX_UrbetrackSync_Status" 
ON "Reporting-Emvarias_14-Sincronizacion_Urbetrack" ("REPEM14_Estado");

-- Keep the LockedAt index as it's still needed
-- (This should already exist, but ensuring it's there)
CREATE INDEX IF NOT EXISTS "IX_UrbetrackSync_LockedAt" 
ON "Reporting-Emvarias_14-Sincronizacion_Urbetrack" ("REPEM14_Bloqueado_En");

-- =====================================================
-- REMOVE PROCESSING STATUS COLUMN
-- =====================================================

-- Remove the ProcessingStatus column (this will be done by the EF Core migration)
-- ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
-- DROP COLUMN IF EXISTS "REPEM14_Estado_Procesamiento";

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify data migration was successful
SELECT 
    "REPEM14_Estado" as Status,
    COUNT(*) as Count
FROM "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
GROUP BY "REPEM14_Estado"
ORDER BY "REPEM14_Estado";

-- Verify indexes were created successfully
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'Reporting-Emvarias_14-Sincronizacion_Urbetrack' 
  AND indexname IN ('IX_UrbetrackSync_Status', 'IX_UrbetrackSync_LockedAt');

-- Check for any remaining locked/processing entries that need attention
SELECT 
    COUNT(*) as remaining_locked_entries,
    COUNT(CASE WHEN "REPEM14_Bloqueado_En" IS NOT NULL THEN 1 END) as entries_with_lock_timestamp
FROM "Reporting-Emvarias_14-Sincronizacion_Urbetrack"
WHERE "REPEM14_Estado" IN ('LockedForCreation', 'LockedForModification', 'LockedForCancellation', 
                          'ProcessingCreation', 'ProcessingModification', 'ProcessingCancellation');

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================

/*
-- To rollback this migration, run the following commands:

-- Re-add the ProcessingStatus column
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
ADD COLUMN "REPEM14_Estado_Procesamiento" text NOT NULL DEFAULT 'Available';

-- Restore ProcessingStatus values based on current SynchronizationStatus
UPDATE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
SET "REPEM14_Estado_Procesamiento" = CASE 
    WHEN "REPEM14_Estado" IN ('LockedForCreation', 'LockedForModification', 'LockedForCancellation') THEN 'Locked'
    WHEN "REPEM14_Estado" IN ('ProcessingCreation', 'ProcessingModification', 'ProcessingCancellation') THEN 'Processing'
    ELSE 'Available'
END;

-- Restore original SynchronizationStatus values
UPDATE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
SET "REPEM14_Estado" = CASE 
    WHEN "REPEM14_Estado" IN ('LockedForCreation', 'ProcessingCreation') THEN 'PendingCreation'
    WHEN "REPEM14_Estado" IN ('LockedForModification', 'ProcessingModification') THEN 'PendingModification'
    WHEN "REPEM14_Estado" IN ('LockedForCancellation', 'ProcessingCancellation') THEN 'PendingCancellation'
    ELSE "REPEM14_Estado"
END;

-- Re-create the ProcessingStatus constraint
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
ADD CONSTRAINT "CK_ProcessingStatus_ValidValues" 
CHECK ("REPEM14_Estado_Procesamiento" IN ('Available', 'Locked', 'Processing'));

-- Drop the new Status-only index
DROP INDEX IF EXISTS "IX_UrbetrackSync_Status";

-- Re-create the composite index
CREATE INDEX "IX_UrbetrackSync_ProcessingStatus_Status" 
ON "Reporting-Emvarias_14-Sincronizacion_Urbetrack" ("REPEM14_Estado_Procesamiento", "REPEM14_Estado");
*/
