-- Manual SQL Migration Script for Pessimistic Locking Fields
-- Migration: AddPessimisticLockingFields
-- Date: 2025-08-27
-- Description: Adds pessimistic locking fields to UrbetrackSynchronization table

-- =====================================================
-- ADD PESSIMISTIC LOCKING COLUMNS
-- =====================================================

-- Add ProcessingStatus column (enum stored as text)
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
ADD COLUMN "REPEM14_Estado_Procesamiento" text NOT NULL DEFAULT 'Available';

-- Add LockedAt timestamp column
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
ADD COLUMN "REPEM14_Bloqueado_En" timestamp without time zone NULL;

-- Add LockedBy GUID column
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
ADD COLUMN "REPEM14_Bloqueado_Por" character varying(100) NULL;

-- =====================================================
-- CREATE PERFORMANCE INDEXES
-- =====================================================

-- Index for lock cleanup queries (finding stale locks)
CREATE INDEX "IX_UrbetrackSync_LockedAt" 
ON "Reporting-Emvarias_14-Sincronizacion_Urbetrack" ("REPEM14_Bloqueado_En");

-- Composite index for pessimistic locking queries (status + processing status)
CREATE INDEX "IX_UrbetrackSync_ProcessingStatus_Status" 
ON "Reporting-Emvarias_14-Sincronizacion_Urbetrack" ("REPEM14_Estado_Procesamiento", "REPEM14_Estado");

-- =====================================================
-- ADD CONSTRAINTS (Optional - for data integrity)
-- =====================================================

-- Ensure ProcessingStatus has valid values
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
ADD CONSTRAINT "CK_ProcessingStatus_ValidValues" 
CHECK ("REPEM14_Estado_Procesamiento" IN ('Available', 'Locked', 'Processing'));

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify columns were added successfully
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'Reporting-Emvarias_14-Sincronizacion_Urbetrack' 
  AND column_name IN ('REPEM14_Estado_Procesamiento', 'REPEM14_Bloqueado_En', 'REPEM14_Bloqueado_Por')
ORDER BY column_name;

-- Verify indexes were created successfully
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'Reporting-Emvarias_14-Sincronizacion_Urbetrack' 
  AND indexname IN ('IX_UrbetrackSync_LockedAt', 'IX_UrbetrackSync_ProcessingStatus_Status');

-- Check current record count and default values
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN "REPEM14_Estado_Procesamiento" = 'Available' THEN 1 END) as available_records,
    COUNT(CASE WHEN "REPEM14_Bloqueado_En" IS NULL THEN 1 END) as unlocked_records
FROM "Reporting-Emvarias_14-Sincronizacion_Urbetrack";

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================

/*
-- To rollback this migration, run the following commands:

-- Drop constraints
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
DROP CONSTRAINT IF EXISTS "CK_ProcessingStatus_ValidValues";

-- Drop indexes
DROP INDEX IF EXISTS "IX_UrbetrackSync_LockedAt";
DROP INDEX IF EXISTS "IX_UrbetrackSync_ProcessingStatus_Status";

-- Drop columns
ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
DROP COLUMN IF EXISTS "REPEM14_Estado_Procesamiento";

ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
DROP COLUMN IF EXISTS "REPEM14_Bloqueado_En";

ALTER TABLE "Reporting-Emvarias_14-Sincronizacion_Urbetrack" 
DROP COLUMN IF EXISTS "REPEM14_Bloqueado_Por";
*/