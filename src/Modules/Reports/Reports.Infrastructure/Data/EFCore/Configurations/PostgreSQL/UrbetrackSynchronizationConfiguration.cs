using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class UrbetrackSynchronizationConfiguration : IEntityTypeConfiguration<UrbetrackSynchronization>
{
    public void Configure(EntityTypeBuilder<UrbetrackSynchronization> builder)
    {
        builder.ToTable(ReportsTableNames.UrbetrackSynchronization);

        builder
            .HasKey(e => e.Id)
            .HasName(UrbetrackSynchronizationColumns.PrimaryKeyConstraintName);
        
        builder
            .Property(e => e.Id)
            .HasColumnName(UrbetrackSynchronizationColumns.Id);
        
        builder
            .Property(e => e.WeighingScaleId)
            .HasColumnName(UrbetrackSynchronizationColumns.WeighingScaleId)
            .IsRequired();
        
        builder
            .Property(e => e.Status)
            .HasColumnName(UrbetrackSynchronizationColumns.Status)
            .HasConversion(new EnumToStringConverter<SynchronizationStatus>())
            .IsRequired();
        
        builder
            .Property(e => e.RetryCount)
            .HasColumnName(UrbetrackSynchronizationColumns.RetryCount)
            .IsRequired();
        
        builder
            .Property(e => e.LastSynchronization)
            .HasColumnName(UrbetrackSynchronizationColumns.LastSynchronization)
            .HasColumnType("timestamp without time zone");
        
        builder
            .Property(e => e.UrbetrackInternalId)
            .HasColumnName(UrbetrackSynchronizationColumns.UrbetrackInternalId)
            .HasMaxLength(50);

        // Pessimistic locking fields (ProcessingStatus removed - now using SynchronizationStatus intermediate states)
        builder
            .Property(e => e.LockedAt)
            .HasColumnName(UrbetrackSynchronizationColumns.LockedAt)
            .HasColumnType("timestamp without time zone");

        builder
            .Property(e => e.LockedBy)
            .HasColumnName(UrbetrackSynchronizationColumns.LockedBy)
            .HasMaxLength(100);

        builder
            .HasOne(e => e.WeighingScale)
            .WithMany()
            .HasForeignKey(e => e.WeighingScaleId)
            .HasConstraintName(UrbetrackSynchronizationColumns.WeighingScaleForeignKey);

        // Index for efficient locking queries (now using only Status since ProcessingStatus is removed)
        builder
            .HasIndex(e => e.Status)
            .HasDatabaseName("IX_UrbetrackSync_Status");

        builder
            .HasIndex(e => e.LockedAt)
            .HasDatabaseName("IX_UrbetrackSync_LockedAt");
    }
}
