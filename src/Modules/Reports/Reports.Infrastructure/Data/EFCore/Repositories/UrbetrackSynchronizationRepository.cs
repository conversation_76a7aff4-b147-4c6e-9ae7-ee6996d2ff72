using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class UrbetrackSynchronizationRepository : Repository<UrbetrackSynchronization, int, ReportsDbContext>, IUrbetrackSynchronizationRepository
{
    public UrbetrackSynchronizationRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }

    public async Task<Dictionary<long, UrbetrackSynchronization>> GetByWeighingScaleIdsAsync(IEnumerable<long> weighingScaleIds, CancellationToken cancellationToken = default)
    {
        return await _context.UrbetrackSynchronizations
            .Where(us => weighingScaleIds.Contains(us.WeighingScaleId))
            .ToDictionaryAsync(us => us.WeighingScaleId, us => us, cancellationToken);
    }

    public async Task<List<UrbetrackSynchronization>> GetByStatusAsync(SynchronizationStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.UrbetrackSynchronizations
            .Where(us => us.Status == status)
            .Include(us => us.WeighingScale)
            .ToListAsync(cancellationToken);
    }

    public async Task BulkInsertAsync(List<UrbetrackSynchronization> synchronizations, CancellationToken cancellationToken = default)
    {
        await _context.UrbetrackSynchronizations.AddRangeAsync(synchronizations, cancellationToken);
    }

    public async Task BulkUpdateAsync(List<UrbetrackSynchronization> synchronizations, CancellationToken cancellationToken = default)
    {
        _context.UrbetrackSynchronizations.UpdateRange(synchronizations);
    }

    public async Task<List<UrbetrackSynchronization>> AcquireLockBatchAsync(
        SynchronizationStatus status,
        int batchSize,
        string lockIdentifier,
        CancellationToken cancellationToken = default)
    {
        var lockedAt = DateTime.UtcNow;
        
        // First, get available entries using LINQ with FOR UPDATE SKIP LOCKED
        var availableEntries = await _context.UrbetrackSynchronizations
            .Where(us => us.Status == status && us.ProcessingStatus == ProcessingStatus.Available)
            .OrderBy(us => us.Id)
            .Take(batchSize)
            .ToListAsync(cancellationToken);

        if (!availableEntries.Any())
            return new List<UrbetrackSynchronization>();

        // Lock the entries
        foreach (var entry in availableEntries)
        {
            entry.AcquireLock(lockIdentifier);
        }

        // Update in database
        _context.UrbetrackSynchronizations.UpdateRange(availableEntries);
        await _context.SaveChangesAsync(cancellationToken);

        // Reload with WeighingScale data
        var entryIds = availableEntries.Select(e => e.Id).ToList();
        var lockedEntries = await _context.UrbetrackSynchronizations
            .Where(us => entryIds.Contains(us.Id))
            .Include(us => us.WeighingScale)
            .ToListAsync(cancellationToken);

        return lockedEntries;
    }

    public async Task ReleaseLockBatchAsync(IEnumerable<UrbetrackSynchronization> entries, CancellationToken cancellationToken = default)
    {
        var entriesList = entries.ToList();
        
        if (!entriesList.Any())
            return;

        // Release locks using entity methods
        foreach (var entry in entriesList)
        {
            entry.ReleaseLock();
        }

        _context.UrbetrackSynchronizations.UpdateRange(entriesList);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<int> CleanupStaleLocks(TimeSpan lockTimeout, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow - lockTimeout;
        
        // Find stale locked entries
        var staleEntries = await _context.UrbetrackSynchronizations
            .Where(us => (us.ProcessingStatus == ProcessingStatus.Locked || us.ProcessingStatus == ProcessingStatus.Processing)
                      && us.LockedAt.HasValue
                      && us.LockedAt.Value < cutoffTime)
            .ToListAsync(cancellationToken);

        if (!staleEntries.Any())
            return 0;

        // Release stale locks
        foreach (var entry in staleEntries)
        {
            entry.ReleaseLock();
        }

        _context.UrbetrackSynchronizations.UpdateRange(staleEntries);
        await _context.SaveChangesAsync(cancellationToken);

        return staleEntries.Count;
    }
}
