using Orion.SharedKernel.Application.Exceptions;
using Quartz;
using Reports.Application.Features.Web;
using Reports.Application.Services.Synchronization;
using Reports.Application.Strategies;
using Reports.Domain;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Infrastructure.Jobs;

public class MainSchedulerJob : IJob
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly SynchronizationStrategyContext _strategyContext;
    private readonly SynchronizationLockManager _lockManager;
    private readonly RetryPolicy _retryPolicy;

    public MainSchedulerJob(
        IReportsUnitOfWork unitOfWork,
        SynchronizationStrategyContext strategyContext,
        SynchronizationLockManager lockManager)
    {
        _unitOfWork = unitOfWork;
        _strategyContext = strategyContext ?? throw new ArgumentNullException(nameof(strategyContext));
        _lockManager = lockManager ?? throw new ArgumentNullException(nameof(lockManager));
        _retryPolicy = RetryPolicy.Default();
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var startTime = DateTime.UtcNow;
        await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Trabajo programado principal iniciado a las {startTime:yyyy-MM-dd HH:mm:ss} UTC",
            new Dictionary<string, object>
            {
                { "StartTime", startTime },
                { "JobType", "MainSchedulerJob" }
            });

        try
        {
            // First cleanup any stale locks before processing
            await _lockManager.CleanupStaleLocksAsync(context.CancellationToken);
            
            await CheckAndScheduleJobsAsync(context.CancellationToken);
            
            var endTime = DateTime.UtcNow;
            var duration = endTime - startTime;
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Trabajo programado principal completado exitosamente a las {endTime:yyyy-MM-dd HH:mm:ss} UTC en {duration.TotalSeconds:F2} segundos",
                new Dictionary<string, object>
                {
                    { "EndTime", endTime },
                    { "Duration", duration.TotalSeconds },
                    { "Status", "Success" }
                });
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error en trabajo programado principal a las {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC: {ex.Message}",
                new Dictionary<string, object>
                {
                    { "ErrorTime", DateTime.UtcNow },
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty },
                    { "Status", "Failed" }
                });
            
            throw new OrionException(
                _unitOfWork
                    .ErrorService
                    .GenerateError(
                        new ScheduledMainJobExecutorFailed(ex.Message)));
        }
    }

    private async Task CheckAndScheduleJobsAsync(CancellationToken cancellationToken = default)
    {
        var checkTime = DateTime.UtcNow;
        await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Iniciando verificación de tareas programadas a las {checkTime:yyyy-MM-dd HH:mm:ss} UTC",
            new Dictionary<string, object>
            {
                { "CheckTime", checkTime }
            });

        try
        {
            var scheduledTasks = await GetDueScheduledTasksAsync(cancellationToken);
            
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Se encontraron {scheduledTasks.Count} tareas programadas listas para ejecución",
                new Dictionary<string, object>
                {
                    { "TaskCount", scheduledTasks.Count },
                    { "Tasks", scheduledTasks.Select(t => new { t.Id, t.Name, t.ScheduledTaskExecutors }).ToList() }
                });

            foreach (var task in scheduledTasks)
            {
                await ExecuteTaskDirectlyAsync(task, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error durante la verificación y ejecución de trabajos: {ex.Message}",
                new Dictionary<string, object>
                {
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty }
                });
        }
    }

    private async Task<List<ScheduledTaskParameter>> GetDueScheduledTasksAsync(CancellationToken cancellationToken)
    {
        try
        {
            var currentTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified);
            
            var tasks = await _unitOfWork.ScheduledTaskParameters.GetAllAsync(
                isPaginated: false,
                predicate: t => t.IsEnabled && (t.NextRunTime == null || t.NextRunTime <= currentTime),
                useCache: false,
                cancellationToken: cancellationToken);
            
            return tasks.Results.ToList();
        }
        catch (InvalidOperationException ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error al convertir valor enum ScheduledTaskExecutors desde la tabla de parámetros de base de datos: {ex.Message}",
                new Dictionary<string, object>
                {
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty },
                    { "ErrorType", "ScheduledTaskExecutorEnumConversionFailed" }
                });
            
            throw new OrionException(
                _unitOfWork
                    .ErrorService
                    .GenerateError(
                        new ScheduledTaskExecutorEnumConversionFailed(ex.Message)));
        }
    }

    private async Task ExecuteTaskDirectlyAsync(ScheduledTaskParameter task, CancellationToken cancellationToken)
    {
        try
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ejecutando tarea con bloqueo pesimista: {task.Name} (Tipo: {task.ScheduledTaskExecutors})",
                new Dictionary<string, object>
                {
                    { "TaskName", task.Name },
                    { "TaskId", task.Id },
                    { "ScheduledTaskExecutors", task.ScheduledTaskExecutors.ToString() }
                });

            var operationType = GetOperationTypeFromJobExecutor(task.ScheduledTaskExecutors);
            var status = GetStatusFromJobExecutor(task.ScheduledTaskExecutors);

            var totalProcessed = 0;
            var totalExceededRetry = 0;
            var batchSummaries = new List<BatchSyncSummary>();

            // Process entries in batches with pessimistic locking
            while (true)
            {
                var processingBatch = await _lockManager.AcquireAndProcessBatchAsync(
                    status,
                    async (lockedEntries, ct) =>
                    {
                        // Filter out entries that exceeded retry limit
                        var validEntries = lockedEntries.Where(entry => !entry.HasExceededRetryLimit(_retryPolicy)).ToList();
                        var exceededRetryEntries = lockedEntries.Where(entry => entry.HasExceededRetryLimit(_retryPolicy)).ToList();

                        if (exceededRetryEntries.Any())
                        {
                            await ProcessExceededRetryEntriesAsync(exceededRetryEntries, ct);
                            totalExceededRetry += exceededRetryEntries.Count;
                        }

                        if (validEntries.Any())
                        {
                            var batchResult = await _strategyContext.ExecuteOperationAsync(
                                operationType,
                                validEntries,
                                _retryPolicy,
                                ct);

                            await SaveBatchResultAsync(batchResult, ct);
                            batchSummaries.Add(batchResult);
                            totalProcessed += batchResult.TotalProcessed;
                        }
                    },
                    cancellationToken);

                // If no entries were acquired, we're done
                if (!processingBatch.Entries.Any())
                {
                    break;
                }

                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"Procesado lote de {processingBatch.Entries.Count} entradas para {task.ScheduledTaskExecutors}",
                    new Dictionary<string, object>
                    {
                        { "BatchSize", processingBatch.Entries.Count },
                        { "ProcessedCount", processingBatch.ProcessedCount },
                        { "ScheduledTaskExecutors", task.ScheduledTaskExecutors.ToString() }
                    });
            }

            await UpdateTaskExecutionTimesAsync(task, cancellationToken);

            // Log final summary
            var combinedSummary = CombineBatchSummaries(batchSummaries);
            await LogExecutionSummaryAsync(task.ScheduledTaskExecutors, combinedSummary, totalExceededRetry);

            if (totalProcessed == 0 && totalExceededRetry == 0)
            {
                await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                    $"No se encontraron entradas pendientes para procesar para {task.ScheduledTaskExecutors}",
                    new Dictionary<string, object>
                    {
                        { "ScheduledTaskExecutors", task.ScheduledTaskExecutors.ToString() },
                        { "Status", status.ToString() }
                    });
            }
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error ejecutando tarea con bloqueo pesimista: {task.Name} (Tipo: {task.ScheduledTaskExecutors}) - {ex.Message}",
                new Dictionary<string, object>
                {
                    { "TaskName", task.Name },
                    { "TaskId", task.Id },
                    { "ScheduledTaskExecutors", task.ScheduledTaskExecutors.ToString() },
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty }
                });
        }
    }

    private static SynchronizationOperationType GetOperationTypeFromJobExecutor(ScheduledTaskExecutors scheduledTaskExecutors)
    {
        return scheduledTaskExecutors switch
        {
            ScheduledTaskExecutors.SyncNewUnloadingTicket => SynchronizationOperationType.Create,
            ScheduledTaskExecutors.SyncUnloadingTicketModification => SynchronizationOperationType.Update,
            ScheduledTaskExecutors.SyncUnloadingTicketDeletion => SynchronizationOperationType.Cancel,
            _ => throw new ArgumentException($"Unsupported job executor: {scheduledTaskExecutors}", nameof(scheduledTaskExecutors))
        };
    }

    private static SynchronizationStatus GetStatusFromJobExecutor(ScheduledTaskExecutors scheduledTaskExecutors)
    {
        return scheduledTaskExecutors switch
        {
            ScheduledTaskExecutors.SyncNewUnloadingTicket => SynchronizationStatus.PendingCreation,
            ScheduledTaskExecutors.SyncUnloadingTicketModification => SynchronizationStatus.PendingModification,
            ScheduledTaskExecutors.SyncUnloadingTicketDeletion => SynchronizationStatus.PendingCancellation,
            _ => throw new ArgumentException($"Unsupported job executor: {scheduledTaskExecutors}", nameof(scheduledTaskExecutors))
        };
    }

    private async Task SaveBatchResultAsync(BatchSyncSummary batchResult, CancellationToken cancellationToken)
    {
        var allEntriesToUpdate = batchResult.ProcessedEntries.Concat(batchResult.FailedEntries).ToList();

        if (allEntriesToUpdate.Any())
        {
            await _unitOfWork.UrbetrackSynchronizations.BulkUpdateAsync(allEntriesToUpdate, cancellationToken);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task LogExecutionSummaryAsync(ScheduledTaskExecutors scheduledTaskExecutors, BatchSyncSummary batchResult, int preFilteredFailedCount)
    {
        var totalProcessedIncludingPreFiltered = batchResult.TotalProcessed + preFilteredFailedCount;
        var totalFailedIncludingPreFiltered = batchResult.FailedDueToRetryLimit + preFilteredFailedCount;
        
        await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Ejecución de {scheduledTaskExecutors} completada. Total procesado: {totalProcessedIncludingPreFiltered}, Éxitos: {batchResult.SuccessCount}, Errores: {batchResult.ErrorCount}, Fallos por límite de reintentos: {totalFailedIncludingPreFiltered}",
            new Dictionary<string, object>
            {
                { "ScheduledTaskExecutors", scheduledTaskExecutors.ToString() },
                { "TotalProcessed", totalProcessedIncludingPreFiltered },
                { "SuccessCount", batchResult.SuccessCount },
                { "ErrorCount", batchResult.ErrorCount },
                { "FailedDueToRetryLimit", totalFailedIncludingPreFiltered },
                { "PreFilteredFailedCount", preFilteredFailedCount },
                { "BatchProcessedCount", batchResult.TotalProcessed }
            });

        if (batchResult.HasErrors || preFilteredFailedCount > 0)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Ejecución de {scheduledTaskExecutors} completada con {batchResult.ErrorCount} errores y {totalFailedIncludingPreFiltered} entradas fallidas permanentemente",
                new Dictionary<string, object>
                {
                    { "ScheduledTaskExecutors", scheduledTaskExecutors.ToString() },
                    { "ErrorCount", batchResult.ErrorCount },
                    { "PermanentlyFailedCount", totalFailedIncludingPreFiltered },
                    { "HasErrors", true }
                });
        }
    }

    private async Task UpdateTaskExecutionTimesAsync(ScheduledTaskParameter task, CancellationToken cancellationToken)
    {
        try
        {
            var currentTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified);
            
            task.LastRunTime = currentTime;
            
            task.NextRunTime = currentTime.AddMinutes(task.FrequencyInMinutes);
            
            _unitOfWork.ScheduledTaskParameters.Update(task);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Tiempos de ejecución actualizados para tarea {task.Name}: Última ejecución={task.LastRunTime:yyyy-MM-dd HH:mm:ss}, Próxima ejecución={task.NextRunTime:yyyy-MM-dd HH:mm:ss}",
                new Dictionary<string, object>
                {
                    { "TaskName", task.Name },
                    { "TaskId", task.Id },
                    { "LastRunTime", task.LastRunTime },
                    { "NextRunTime", task.NextRunTime },
                    { "FrequencyInMinutes", task.FrequencyInMinutes }
                });
        }
        catch (Exception ex)
        {
            await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
                $"Error actualizando tiempos de ejecución para tarea: {task.Name} (ID: {task.Id}) - {ex.Message}",
                new Dictionary<string, object>
                {
                    { "TaskName", task.Name },
                    { "TaskId", task.Id },
                    { "ErrorMessage", ex.Message },
                    { "StackTrace", ex.StackTrace ?? string.Empty }
                });
        }
    }

    private static BatchSyncSummary CombineBatchSummaries(List<BatchSyncSummary> batchSummaries)
    {
        if (!batchSummaries.Any())
            return new BatchSyncSummary();

        var combinedSummary = new BatchSyncSummary();
        
        foreach (var summary in batchSummaries)
        {
            foreach (var processedEntry in summary.ProcessedEntries)
            {
                combinedSummary.AddProcessedEntry(processedEntry);
            }
            
            foreach (var failedEntry in summary.FailedEntries)
            {
                combinedSummary.AddFailedEntry(failedEntry);
            }
        }

        return combinedSummary;
    }

    private async Task ProcessExceededRetryEntriesAsync(
        List<UrbetrackSynchronization> exceededRetryEntries,
        CancellationToken cancellationToken)
    {
        foreach (var failedEntry in exceededRetryEntries)
        {
            failedEntry.Status = failedEntry.Status switch
            {
                SynchronizationStatus.PendingCreation => SynchronizationStatus.CreationFailure,
                SynchronizationStatus.PendingModification => SynchronizationStatus.UpdateFailure,
                SynchronizationStatus.PendingCancellation => SynchronizationStatus.CancellationFailure,
                _ => failedEntry.Status
            };
        }
        
        await _unitOfWork.UrbetrackSynchronizations.BulkUpdateAsync(exceededRetryEntries, cancellationToken);
        
        await _unitOfWork.LogEventMessage.GenerateLogEventMessage(
            $"Se marcaron {exceededRetryEntries.Count} entradas como error permanente por exceder el límite de reintentos ({_retryPolicy.MaxRetryCount})",
            new Dictionary<string, object>
            {
                { "FailedEntriesCount", exceededRetryEntries.Count },
                { "MaxRetryCount", _retryPolicy.MaxRetryCount },
                { "FailedEntryIds", exceededRetryEntries.Select(e => e.Id).ToList() }
            });
    }
}