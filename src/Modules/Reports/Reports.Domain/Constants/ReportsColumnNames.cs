namespace Reports.Domain.Constants;

public static class TownColumns
{
    public const string Code = "REPEM01_Codigo";
    public const string Name = "REPEM01_Nombre";
    public const string Department = "REPEM01_Departamento";
    public const string Province = "REPEM01_Provincia";

    public const string PrimaryKeyConstraintName = ReportsTableNames.Town + "_key";
}

public static class RecyclingAreaColumns
{
    public const string Code = "REPEM02_Codigo";
    public const string Name = "REPEM02_Nombre";

    public const string PrimaryKeyConstraintName = ReportsTableNames.RecyclingArea + "_key";
}

public static class WeighingScaleColumns
{
    public const string Id = "REPEM03_Id";
    public const string LicensePlate = "REPEM03_Patente";
    public const string NIT = "REPEM03_Nro_Identificacion_Tributaria";
    public const string ArrivingWeight = "REPEM03_Peso_de_entrada";
    public const string LeavingWeight = "REPEM03_Peso_de_salida";
    public const string EntryDate = "REPEM03_Fecha_de_entrada";
    public const string EgressDate = "REPEM03_Fecha_de_egreso";
    public const string MaterialType = "REPEM03_Tipo_de_material";
    public const string DepositPlace = "REPEM03_Lugar_de_deposito";
    public const string OriginType = "REPEM03_Tipo_de_origen";
    public const string NUAP = "REPEM03_Nro_Unico_Area_Prestacion";
    public const string LoadingDate = "REPEM03_Fecha_de_carga";
    public const string CancelDate = "REPEM03_Fecha_de_cancelacion";
    public const string LoadingType = "REPEM03_Tipo_de_carga";
    public const string Town = "REPEM03_Municipio";

    public const string PrimaryKeyConstraintName = ReportsTableNames.WeighingScale + "_key";
    public const string WeighingScaleTownConstraintName = "Reporting-Emvarias_REPEM03-REPEM01_fkey";
}

public static class CollectionByMicrorouteColumns
{
    public const string Id = "REPEM04_Id";
    public const string LicensePlate = "REPEM04_Patente";
    public const string ServiceStatus = "REPEM04_EstadoServicio";
    public const string GroupTurn = "REPEM04_GrupoTurno";
    public const string ServiceType = "REPEM04_TipoServicio";
    public const string RouteCode = "REPEM04_RutaCodigo";
    public const string IsReinforcement = "REPEM04_EsRefuerzo";
    public const string Intern = "REPEM04_Interno";
    public const string TotalWeight = "REPEM04_PesoTotal";
    public const string ServiceId = "REPEM04_IdServicio";
    public const string RouteArrivalDate = "REPEM04_FechaHora_EntradaRuta";
    public const string RouteDepartureDate = "REPEM04_FechaHora_SalidaRuta";
    public const string Observations = "REPEM04_Observaciones";
    public const string TotalTonnage = "REPEM04_PesoTotal_Toneladas";
    public const string WeighingDate = "REPEM04_FechaHora_Pesaje";
    public const string ServicingDate = "REPEM04_Fecha_de_Servicio";
    public const string ServiceStartDate = "REPEM04_FechaHora_InicioServicio";
    public const string BaseArrivalDate = "REPEM04_FechaHora_LlegadaBase";
    public const string BaseDepartureDate = "REPEM04_FechaHora_SalidaBase";

    public const string PrimaryKeyConstraintName = ReportsTableNames.CollectionByMicroroute + "_key";
}

public static class VehicleRetrievalColumns
{
    public const string NUAP = "REPEM05_NUAP";
    public const string Year = "REPEM05_Año";
    public const string Month = "REPEM05_Mes";
    public const string Tons = "REPEM05_Toneladas";

    public const string PrimaryKeyConstraintName = ReportsTableNames.VehicleRetrieval + "_key";
}

public static class TollColumns
{
    public const string Id = "REPEM06_Id";
    public const string VehicleType = "REPEM06_Codigo_Tipo_Vehiculo";
    public const string Value = "REPEM06_Valor";
    public const string LicensePlate = "REPEM06_Placa";
    public const string ValidFromDate = "REPEM06_Fecha_Validez";

    public const string PrimaryKeyConstraintName = ReportsTableNames.Toll + "_key";
}

public static class MicroRouteColumns
{
    public const string MicrorouteCode = "REPEM07_Numero_de_Microruta";
    public const string ExternalRouteCode = "REPEM07_Numero_Ruta_Intendencia";
    public const string ExtendedRouteCode = "REPEM07_Ruta_Larga";
    public const string UrbanCleaningPercentage = "REPEM07_Porcentaje_Limpieza_Urbana";
    public const string SweepingPercentage = "REPEM07_Porcentaje_Barrido";
    public const string NonAforatedPercentage = "REPEM07_Porcentaje_No_Aforado";
    public const string RecyclableTonsPercentage = "REPEM07_Porcentaje_Residuos_Aprovechables";
    public const string NUAP = "REPEM07_NUAP";
    public const string StartDateValidity = "REPEM07_Fecha_Inicio_Vigencia";
    public const string EndDateValidity = "REPEM07_Fecha_Fin_Vigencia";

    public const string PrimaryKeyConstraintName = ReportsTableNames.MicroRoute + "_key";
}

public static class ClientColumns
{
    public const string NIT = "REPEM08_NIT";
    public const string FullName = "REPEM08_Nombre_Completo";

    public const string PrimaryKeyConstraintName = ReportsTableNames.Client + "_key";
}

public static class RejectionColumns
{
    public const string Id = "REPEM09_Id";
    public const string LicensePlate = "REPEM09_Placa";
    public const string RejectionDate = "REPEM09_Fecha_Corta";
    public const string ExtendedRouteCode = "REPEM09_Ruta_Larga";
    public const string ECA = "REPEM09_ECA";
    public const string Tonnage = "REPEM09_Toneladas";

    public const string PrimaryKeyConstraintName = ReportsTableNames.Rejections + "_key";
    public const string RejectionCompoundIndexName = "REPEM09_Fecha_Corta_Placa_idx";
}

public static class GeneratedServiceTicketColumns
{
    public const string Id = "REPEM10_Id";
    public const string Weight = "REPEM10_Valor";

    public const string PrimaryKeyConstraintName = ReportsTableNames.GeneratedTickets + "_key";
}

public static class ReportFormat14ViewColumns
{
    public const string NUAP = "C1_NUAP";
    public const string DestinationType = "C2_TIPO_SITIO";
    public const string DestinationCode = "C3_NUSD";
    public const string LicensePlate = "C4_PLACA";
    public const string VehicleArrival = "C5_FECHA";
    public const string VehicleArrivalTime = "C6_HORA";
    public const string MicrorouteId = "C7_NUMICRO";
    public const string UrbanCleaningTons = "C8_TON_LIMP_URB";
    public const string SweepingTons = "C9_TON_BARRIDO";
    public const string NonRecyclableTons = "C10_TONRESNA";
    public const string RejectedTons = "C11_TONRECHAPR";
    public const string RecyclableTons = "C12_TONRESAPR";
    public const string MeasuringUnit = "C13_SISTEMA_MEDICION";
    public const string Toll = "C14_VLRPEAJ";
    public const string ServiceTicketId = "CE_ID_TICKET";
    public const string RecyclingArea = "CE_NOMBRE_AREA";
    public const string ExtendedRouteCode = "CE_RUTA_LARGA";
    public const string TotalTons = "CE_TON_TOTAL";
    public const string CompensationRelation = "CE_REL_COMPENSACION";
    public const string CompensationRelationTicketId = "CE_REL_COMPENSACION_ID_TICKET";
}

public static class ReportFormat34ViewColumns
{
    public const string NUSD = "C1_NUSD";
    public const string OriginType = "C2_TIPO_ORIGEN";
    public const string PlaceOriginNumber = "C3_NUSITIO_ORI";
    public const string CompanyName = "C4_NOMBRE_EMPRESA";
    public const string NIT = "C5_NIT_EMPRESA";
    public const string DaneCode = "C6_COD_DANE_ORI";
    public const string LicensePlate = "C7_PLACA";
    public const string ArrivalDate = "C8_FECHA_INGRESO";
    public const string DepartureDate = "C9_FECHA_SALIDA";
    public const string ArrivalTime = "C10_HORA_INGRESO";
    public const string DepartureTime = "C11_HORA_SALIDA";
    public const string Tons = "C12_TONELADAS";
    public const string ServiceTicketId = "CE_ID_TICKET";
    public const string ServiceTicketWeight = "CE_VALOR_TICKET_LEGACY";
    public const string ServiceTicketTonnage = "CE_VALOR_TON_TICKET_LEGACY";
    public const string RejectedTonnage = "CE_TONELADAS_RECHAZADAS";
    public const string ExistsInReport14 = "CE_EXISTE_EN_F14";
    public const string CompanyNIT = "CE_NIT_EMPRESA";
    public const string NUAP = "CE_NUAP";
    public const string FilteredDate = "CE_FECHA_FILTRO";
}

public static class DistributionsViewColumns
{
    public const string Year = "Año";
    public const string Month = "Mes";
    public const string NUAP = "NUAP";
    public const string RecyclingArea = "Area_Aprovechamiento";
    public const string Trips = "Cantidad_de_Viajes";
    public const string DeviationTons = "Toneladas_Desviacion";
    public const string DistributedTons = "Toneladas_Distribuidas";
    public const string TollSharedRouteTons = "Toneladas_Rutas_Compartidas";
    public const string DistributionTollPercentage = "Porcentaje_Distribucion_Peaje";
    public const string ReportedTons = "Toneladas_Reportadas";
}

public static class HistoricalAuditColumns
{
    public const string Id = "REPEM13_Id";
    public const string EntityId = "REPEM13_Id_Entidad";
    public const string TableName = "REPEM13_Nombre_Tabla";
    public const string ActionType = "REPEM13_Tipo_Accion";
    public const string User = "REPEM13_Usuario";
    public const string ActionDate = "REPEM13_Fecha_Accion";
    public const string PreviousData = "REPEM13_Datos_Previos";

    public const string PrimaryKeyConstraintName = ReportsTableNames.HistoricalAudit + "_key";
}

public static class UrbetrackSynchronizationColumns
{
    public const string Id = "REPEM14_Id";
    public const string WeighingScaleId = "REPEM14_Id_Pesaje_Balanza";
    public const string Status = "REPEM14_Estado";
    public const string RetryCount = "REPEM14_Nro_Reintentos";
    public const string LastSynchronization = "REPEM14_Ultima_Sincronizacion";
    public const string UrbetrackInternalId = "REPEM14_Id_Interno_Urbetrack";
    
    // Pessimistic locking columns (ProcessingStatus removed - now using SynchronizationStatus intermediate states)
    public const string LockedAt = "REPEM14_Bloqueado_En";
    public const string LockedBy = "REPEM14_Bloqueado_Por";

    public const string PrimaryKeyConstraintName = ReportsTableNames.UrbetrackSynchronization + "_key";
    public const string WeighingScaleForeignKey = "FK_REPEM14_REPEM03";
}

public static class ScheduledTaskParameterColumns
{
    public const string Id = "REPEM15_Id";
    public const string Name = "REPEM15_Nombre";
    public const string Description = "REPEM15_Descripcion";
    public const string JobExecutor = "REPEM15_Instancia_Ejecutora";
    public const string IsEnabled = "REPEM15_Habilitado";
    public const string FrequencyInMinutes = "REPEM15_Frecuencia_en_Minutos";
    public const string LastRunTime = "REPEM15_Ultima_Ejecucion";
    public const string NextRunTime = "REPEM15_Proxima_Ejecucion";

    public const string PrimaryKeyConstraintName = ReportsTableNames.ScheduledTaskParameter + "_key";
}