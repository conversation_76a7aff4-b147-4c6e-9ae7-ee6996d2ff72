using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;
using Reports.Domain.ValueObjects;

namespace Reports.Domain.Entities;

public class UrbetrackSynchronization : Entity<int>
{
    public long WeighingScaleId { get; set; }
    public WeighingScale WeighingScale { get; set; }
    public SynchronizationStatus Status { get; set; }
    public int RetryCount { get; set; }
    public DateTime? LastSynchronization { get; set; }
    public string? UrbetrackInternalId { get; set; }
    
    // Pessimistic locking fields
    public ProcessingStatus ProcessingStatus { get; set; } = ProcessingStatus.Available;
    public DateTime? LockedAt { get; set; }
    public string? LockedBy { get; set; }
    
    public void ApplySynchronizationResult(SyncOperationOutcome result)
    {
        if (result == null)
            throw new ArgumentNullException(nameof(result));

        Status = result.Status;

        if (result.IsSuccess)
        {
            UrbetrackInternalId = result.UrbetrackInternalId;
            LastSynchronization = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Unspecified);
        }
        else if (result.ShouldRetry)
        {
            RetryCount++;
        }
    }

    public bool HasExceededRetryLimit(RetryPolicy retryPolicy)
    {
        var currentRetryPolicy = new RetryPolicy(retryPolicy.MaxRetryCount, RetryCount);
        return currentRetryPolicy.HasExceededLimit;
    }

    public bool IsLocked()
    {
        return ProcessingStatus == ProcessingStatus.Locked || ProcessingStatus == ProcessingStatus.Processing;
    }

    public bool IsLockExpired(TimeSpan lockTimeout)
    {
        if (!IsLocked() || !LockedAt.HasValue)
            return false;

        return DateTime.UtcNow - LockedAt.Value > lockTimeout;
    }

    public void AcquireLock(string lockIdentifier)
    {
        ProcessingStatus = ProcessingStatus.Locked;
        LockedAt = DateTime.UtcNow;
        LockedBy = lockIdentifier;
    }

    public void StartProcessing()
    {
        if (ProcessingStatus != ProcessingStatus.Locked)
            throw new InvalidOperationException("Cannot start processing on an unlocked entry");
        
        ProcessingStatus = ProcessingStatus.Processing;
    }

    public void ReleaseLock()
    {
        ProcessingStatus = ProcessingStatus.Available;
        LockedAt = null;
        LockedBy = null;
    }
}
